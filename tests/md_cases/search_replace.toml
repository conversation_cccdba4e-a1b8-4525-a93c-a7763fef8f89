[given]
md = '''
# Original Title

This is a paragraph with text.

```original-language
original code content here
```

Here are some list items:

- Item with **bold text** formatting
- Item with _emphasis and **nested bold**_ formatting
- ![original image alt](https://example.com/original/image.png) description
- [original link text](https://example.com/original/page.html) description
'''

[chained]
needed = false

[expect."search-replace section title"]
cli_args = ['--link-format', 'keep', '# !s/Original/New/']
output = '''
# New Title

This is a paragraph with text.

```original-language
original code content here
```

Here are some list items:

- Item with **bold text** formatting
- Item with _emphasis and **nested bold**_ formatting
- ![original image alt](https://example.com/original/image.png) description
- [original link text](https://example.com/original/page.html) description
'''

[expect."search-replace code block language"]
cli_args = ['```!s/original-.*/python/']
output = '''
```python
original code content here
```
'''

[expect."search-replace code block contents"]
cli_args = ['``` !s/original/new/']
output = '''
```original-language
new code content here
```
'''

[expect."search-replace image alt text"]
cli_args = ['![ !s/original/new/ ]()']
output = '''![new image alt][1]

[1]: https://example.com/original/image.png
'''

[expect."search-replace image alt text with non-matching"]
cli_args = ['![ !s/BOGUS/new/ ]()']
expect_success = false
output = ''

[expect."search-replace image url"]
cli_args = ['![](!s/original/new/)']
output = '''![original image alt][1]

[1]: https://example.com/new/image.png
'''

[expect."search-replace link text"]
cli_args = ['[ !s/original/new/ ]()']
output = '''[new link text][1]

[1]: https://example.com/original/page.html
'''

[expect."search-replace link url"]
cli_args = ['[]( !s/original/new/ )']
output = '''[original link text][1]

[1]: https://example.com/new/page.html
'''

[expect."search-replace straightforward formatting"]
cli_args = ['- !s/bold/strong/']
output = '''- Item with **strong text** formatting

   -----

- Item with _emphasis and **nested strong**_ formatting
'''

[expect."search-replace nested formatting"]
cli_args = ['- !s/and nested /and formerly /']
output = '''- Item with _emphasis and formerly **bold**_ formatting
'''

[expect."search-replace paragraph text"]
cli_args = ['P: !s/paragraph/text/']
output = '''This is a text with text.
'''
