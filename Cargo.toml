[package]
authors = ["<PERSON><PERSON>t <<EMAIL>>"]
name = "mdq"
version = "0.10.0-dev"
edition = "2021"
license = "MIT OR Apache-2.0"
description = "Select and render specific elements in a Markdown document"
repository = "https://github.com/yshavit/mdq"
keywords = ["markdown", "parsing"]
categories = ["command-line-utilities", "text-editors"]
rust-version = "1.80.1"

[dependencies]
clap = { version = "4.5.7", features = ["derive"] }
derive_builder = "0.20.2"
markdown = "1.0.0"
memchr = "2.7.4"
paste = "1.0"
pest = "2.8"
pest_derive = { version = "2.8", features = ["grammar-extras"] }
fancy-regex = "0.16"
serde = { version = "1", features = ["derive"] }
serde_json = "1.0"

[dev-dependencies]
indoc = "2"
lazy_static = "1.4.0"

[build-dependencies]
indoc = "2"
serde = { version = "1", features = ["derive"] }
toml = "0.9"

[profile.release]
codegen-units = 1
lto = true
